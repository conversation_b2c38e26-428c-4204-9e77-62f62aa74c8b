<source>
    @type tail
    encoding utf-8
    from_encoding utf-8
    path /fluentd/log/the*.log 
    pos_file /fluentd/log/thelog.pos
    tag fluentd.log.the.log
    path_key path
    format /^(?<exectime>\d{4}/\d{1,2}/\d{1,2}\s\d{1,2}:\d{1,2}:\d{1,2}) ?;(?<module>.*?);(?<loglevel>.*?);(?<lineno>.*?);(?<operator>.*?);(?<action>.*?);(?<object>.*?);(?<role>.*?);(?<result>.*?)$/
</source>

<filter fluentd.log.the.log>
  @type record_transformer
  <record>
    client_address "#{IPSocket.getaddress(Socket.gethostname)}"
  </record>
</filter>

<match fluentd.log.the.log>
    @type copy    				#match模块表示日志再转发
    <store>
	@type elasticsearch    			#转发日志到es，这需要安装es的插件
	hosts *************:9200    		#es的配置信息
	logstash_format true    		#启动转发
	logstash_prefix thelog
	logstash_dateformat %Y.%m.%d    	#按日来划分索引，可以删除无用数据
	flush_interval 5s    			#每5s刷新一次
    </store>
    ########## mark label line donot delete ##############
    ###########center server configure start #############
    <store>
	@type elasticsearch
	hosts **************:9200
	logstash_format true
	logstash_prefix thelog
	logstash_dateformat %Y.%m.%d
	flush_interval 5s
    </store>
    ###########center server configure end ###############
 
    <store>
	@type stdout 
    </store>
</match>
