from tornado_swagger.components import components

@components.schemas.register
class EventFormModel(object):
    """
    ---
    type: object
    description: 事件查询条件
    properties:
        username:
            type: string
            description: 用户登录名
        level:
            type: string
            description: 事件等级
        op:
            type: string
            description: 操作
        start_date:
            type: string
            description: 开始时间
        end_date:
            type: string
            description: 结束时间


    """

@components.schemas.register
class EventListModel(object):
    """
    ---
    type: array
    description: Array of Post model representation
    items:
        $ref: '#/components/schemas/EventModel'
    """


@components.schemas.register
class EventModel(object):
    """
    ---
    type: object
    description: 创建用户
    properties:
        id:
            type: string
            description: 用户ID
        name:
            type: string
            description: 用户昵称
        username:
            type: string
            description: 用户登录名
        level:
            type: string
            description: 事件等级
        op:
            type: string
            description: 操作
        desc:
            type: string
            description: 操作描述
        create_at:
            type: string
            description: 创建时间
            
    """  




