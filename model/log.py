from enum import Enum
from typing import Dict, List


# 定义操作类别的枚举
class OperationCategory(Enum):
    LOGIN = "登录认证"
    PHYSICAL_MACHINE_MANAGEMENT = "物理机管理"
    VIRTUAL_MACHINE_MANAGEMENT = "虚机管理"
    VIRTUAL_MACHINE_OPERATION = "虚机操作"
    IMAGE_MANAGEMENT = "镜像管理"
    # RECYCLE_BIN_MANAGEMENT = "回收站管理"
    STORAGE_MANAGEMENT = "存储管理"
    SNAPSHOT_MANAGEMENT = "快照管理"
    SECURITY_GROUP = "安全组"
    CLUSTER_ALERT = "集群告警"
    STORAGE_ALERT = "存储告警"
    NETWORK_MANAGEMENT = "网络管理"
    USER_MANAGEMENT = "用户管理"
    SYSTEM_FUNCTION = "系统功能"


# 定义操作结果的枚举
class OperationResult(Enum):
    SUCCESS = "成功"
    FAILURE = "失败"


# 定义操作角色的枚举
class OperationRole(Enum):
    ADMIN = "管理员"
    SECURITY_ADMIN = "安全员"
    AUDITOR = "审计员"
    OPERATOR = "操作员"


# 定义具体操作的枚举
class Action(Enum):
    LOGIN = "登录"
    ADD_CLUSTER = "创建主机集群"
    DELETE_CLUSTER = "删除主机集群"
    UPDATE_CLUSTER_NAME = "修改主机集群名称"
    ADD_HOST_TO_CLUSTER = "添加主机到集群"
    REMOVE_HOST_FROM_CLUSTER = "从主机集群移除主机"
    CREATE_VM_GROUP = "创建虚拟机分组"
    DELETE_VM_GROUP = "删除虚拟机分组"
    UPDATE_VM_GROUP_NAME = "编辑虚拟机组"
    ADD_VM_TO_GROUP = "虚拟机移入分组"
    REMOVE_VM_FROM_GROUP = "从分组移除虚拟机"
    CREATE_VM = "创建虚拟机"
    ALLOCATE_VM = "分配虚拟机"
    RENAME_VM = "重命名虚拟机"
    # CLONE_VM = "克隆"
    DELETE_VM_TO_RECYCLE_BIN = "删除虚拟机"
    UPDATE_VM_CONFIG = "虚拟机调整配置"
    UPDATE_VM_IP = "修改IP"
    ADD_VM_NETPORT = "增加IP"
    DELETE_VM_NETPORT = "删除IP"
    SELECT_SECURITY_GROUP = "选择安全组"
    ATTACH_VOLUME = "挂载云硬盘到虚拟机"
    DETACH_VOLUME = "分离云硬盘"
    START_VM = "虚拟机开机"
    SHUTDOWN_VM = "虚拟机关机"
    RESTART_VM = "重启虚拟机"
    SUSPEND_VM = "挂起虚拟机"
    RESUME_VM = "取消挂起虚拟机"
    MIGRATE_VM = "虚拟机迁移"
    MIGRATE_LIVE_VM = "虚拟机热迁移"
    SECURITY_GROUPS = "选择安全组"
    SWITCH_SYSTEM_TYPE = "切换系统类型"
    CREATE_IMAGE = "上传镜像"
    DELETE_IMAGE = "删除镜像"
    EDIT_IMAGE = "重命名镜像"
    SWITCH_IMAGE_TYPE = "切换系统类型"
    RESTORE_VM = "还原虚拟机"
    PERMANENTLY_DELETE_VM = "强制删除虚拟机"
    CREATE_VOLUME = "创建云硬盘"
    DELETE_VOLUME = "删除云硬盘"
    EDIT_VOLUME = "编辑云硬盘"
    # CREATE_VOLUME_SNAPSHOT = "云硬盘创建虚拟机"
    EXPAND_VOLUME = "调整大小"
    CREATE_SNAPSHOT = "创建快照"
    DELETE_SNAPSHOT = "删除快照"
    # BACKUP_SNAPSHOT = "创建备份"
    EDIT_SNAPSHOT = "编辑快照"
    ADD_SECURITY_RULE = "安全组创建规则"
    DELETE_SECURITY_RULE = "安全组删除规则"
    DELETE_SECURITY_GROUP = "删除安全组"
    IGNORE_CLUSTER_ALERT = "忽略集群告警"
    IGNORE_STORAGE_ALERT = "忽略存储告警"
    CREATE_NETWORK = "创建网络"
    EDIT_NETWORK = "修改网络"
    ADD_SUBNET = "创建子网"
    EDIT_SUBNET = "修改子网"
    DELETE_SUBNET = "删除子网"
    DELETE_NETWORK = "删除网络"
    ADD_USER = "新增用户"
    EDIT_USER = "修改用户"
    DELETE_USER = "删除用户"
    # UPDATE_USER_STATUS = "状态修改"
    RESET_PASSWORD = "重置密码"
    ENABLE_AUTO_EVACUATE = "自动宕机迁移"
    CHANGE_PASSWORD = "修改密码"


def get_enum_values(enum_class: Enum) -> List[Dict[str, str]]:
    return [{"name": item.name, "value": item.value} for item in enum_class]