执行步骤：
编译podman镜像
1，cd fluentd
sh run.sh
启动flentd服务
2. cd ..
sh run.sh
3,访问*************:5601 kibana页面，查看结果

EFK-fluent目录：
部署fluent客户端
EFK目录：
部署es，kibana服务

部署注意事项：
**************设备所做操作：
1，/etc/hosts
增加：************** theweb
2，布置定时任务
crontab -e
*/5 * * * * /root/thenew/cs.sh 2&>1 >> /var/log/cscron.log
3，配置日志文件管理
在
/etc/logrotate.d目录下
[root@theweb logrotate.d]# cat configes 
/var/log/cscron.log
{
size 1M
notifempty
copytruncate
rotate 5
missingok
compress
dateext
dateformat .%Y%m%d
}
4，/root/thenew/data/etc/fluent.conf
保留不删除行：
########## mark label line donot delete ##############
