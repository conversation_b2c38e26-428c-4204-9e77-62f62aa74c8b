from elasticsearch import Elasticsearch
import json
import os

class ESUtil(object):
    def __init__(self, host, port, timeout):
        self.array = []
        self.uniquePath = []
        self.uniqueResult = []
        self.uniqueAction = []
        self.index = ""
        self.host = host
        self.port = port
        self.timeout = timeout
        self.result=""
        self.handler = Elasticsearch([{"host":self.host, 'port': self.port}], timeout=self.timeout)
    def setIndex(self, index):
        self.index = index

    def UniqueField(self):
        body = {
                'query': {
                    'match': {
                        'path':'theauth.log'
                        }
                    },
                'size':200
                }
        self.result = self.handler.search(index=self.index, body=body, size=200)

    def AllField(self):
        body = {
                'query': {
                    'match_all': {}
                    }
                }
        self.result = self.handler.search(index=self.index, body=body, size=200)

    def get_page_data(self, result):
        for hit in result['hits']['hits']:
              print(hit)

    def UniquePath(self):
        self.AllField()
        self.array.clear()
        for hit in self.result['hits']['hits']:
            #self.array.append(os.path.basename(hit['_source']['path']))
            self.array.append(hit['_source']['path'])
        self.uniquePath = list(set(self.array))
        #print(self.uniquePath)
        return {"path":self.uniquePath}

    def UniqueResult(self):
        self.AllField()
        self.array.clear()
        for hit in self.result['hits']['hits']:
            self.array.append(hit['_source']['result'])
        self.uniqueResult = list(set(self.array))
        #print(self.uniqueResult)
        return {"result":self.uniqueResult}

    def UniqueAction(self):
        self.AllField()
        self.array.clear()
        for hit in self.result['hits']['hits']:
            self.array.append(hit['_source']['action'])
        self.uniqueAction = list(set(self.array))
        #print(self.uniqueAction)
        return {"action":self.uniqueAction}

    def UniqueCategory(self):
        self.AllField()
        self.array.clear()
        for hit in self.result['hits']['hits']:
            self.array.append(hit['_source']['category'])
        self.uniqueCategory = list(set(self.array))
        return {"category": self.uniqueCategory}

    def UniqueRole(self):
        self.AllField()
        self.array.clear()
        for hit in self.result['hits']['hits']:
            self.array.append(hit['_source']['role'])
        self.uniqueRole = list(set(self.array))
        return {"role": self.uniqueRole}



"""
if __name__=='__main__':
    #index='thelog-2022.06.28'
    index='thelog-*'
    es = ESUtil(index, "192.168.203.71", 9200, 3600)
    print(es.UniquePath())
    print(es.UniqueResult())
    print(es.UniqueAction())
"""
