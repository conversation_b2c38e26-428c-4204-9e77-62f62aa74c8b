#!/bin/sh

ES=$(curl -s http://**************:8093/v1/cloudy/master)
#ES={}
FLUENTCONF="fluent.conf"
ESIP=$(echo ${ES} | jq .cloudy_ip | sed $'s/\"//g')
ESPORT=$(echo ${ES} | jq .cloudy_port | sed $'s/\"//g')
if [ ${ESIP} != "null" -a ${ESPORT} != "null" ]; then
	VAL=$(grep "center server configure start" ${FLUENTCONF})
	if [ $? -eq 1 ]; then
		sed -i "/mark label line donot delete/a\    ###########center server configure start #############\n    <store>\n       @type elasticsearch\n       hosts ${ESIP}:${ESPORT}\n       logstash_format true\n       logstash_prefix thelog\n       logstash_dateformat %Y.%m.%d\n       flush_interval 5s\n    </store>\n    ###########center server configure end ###############\n" ${FLUENTCONF}
		if [ $? -eq 0 ]; then
			DT=`date '+%Y-%m-%d %H:%M:%S'`
			echo ${DT} " add es server configure successful"
		else
			echo ${DT} " add es server configure failure"
		fi
	fi
else
	VAL=$(grep "center server configure end" ${FLUENTCONF})
	if [ $? -eq 0 ]; then
		sed -i '/mark label line donot delete/, +10{/mark label line donot delete/b; d}' ${FLUENTCONF} 
		if [ $? -eq 0 ]; then
			DT=`date '+%Y-%m-%d %H:%M:%S'`
			echo ${DT} " delete es server configure successful"
		else
			echo ${DT} " delete es server configure failure"
		fi
	fi	
fi
