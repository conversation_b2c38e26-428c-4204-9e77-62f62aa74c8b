FROM docker.io/fluent/fluentd:v1.11-1

USER root

RUN echo "source 'https://mirrors.tuna.tsinghua.edu.cn/rubygems/'" > Gemfile && gem install bundler
RUN apk add -U tzdata
RUN cp /usr/share/zoneinfo/Asia/Shanghai /etc/localtime

RUN gem install elasticsearch -v 6.8.3
RUN gem install fluent-plugin-elasticsearch \
fluent-plugin-tail-ex \
fluent-plugin-tail-multiline

USER root

CMD ["fluentd", "-o", "/fluentd/log/fluentd.log", "--log-rotate-age", "daily", "--log-rotate-size", "102400"]
