# coding=utf-8
from elasticsearch import Elasticsearch
import json
import os
import datetime
import time
import logging

class ESUtil(object):
    def __init__(self, index, host, port, timeout):
        self.array = []
        self.uniquePath = []
        self.uniqueResult = []
        self.uniqueAction = []
        self.index = index
        self.host = host
        self.port = port
        self.timeout = timeout
        self.result=""
        self.handler = Elasticsearch([{"host":self.host, 'port': self.port}], timeout=self.timeout)

    def UniqueField(self):
        body = {
                'query': {
                    'match': {
                        'path':'theauth.log'
                        }
                    },
                'size':200
                }
        self.result = self.handler.search(index=self.index, doc_type='fluentd', body=body, size=200)

    def AllField(self):
        body = {
                'query': {
                    'match_all': {}
                    }
                }
        self.result = self.handler.search(index=self.index, doc_type='fluentd', body=body, size=200)
        print(self.result)

    def indexExist(self, index):
        for i in es.handler.indices.get('thelog-*'):
            if i == index:
                return True
        return False

    def MakeIndex(self, starttime, endtime):
        startTimeArray = time.strptime(starttime, "%Y/%m/%d %H:%M:%S")
        endTimeArray = time.strptime(endtime, "%Y/%m/%d %H:%M:%S")
        startTimeStamp = str(int(time.mktime(startTimeArray)))+"000"
        endTimeStamp = str(int(time.mktime(endTimeArray)))+"000"

        startIndexTime = time.strftime("%Y.%m.%d", startTimeArray)
        endIndexTime = time.strftime("%Y.%m.%d", endTimeArray)

        startDateArray = time.strptime(startIndexTime, "%Y.%m.%d")
        endDateArray = time.strptime(endIndexTime, "%Y.%m.%d")
        startDateStamp = int(time.mktime(startDateArray))
        endDateStamp = int(time.mktime(endDateArray))

        indexList=[]
        if startDateStamp == endDateStamp:
            oneday=time.strftime('%Y.%m.%d', time.localtime(startDateStamp))
            index = "thelog-"+oneday
            if True == self.indexExist(index):
                indexList.append(index)
        elif endDateStamp > startDateStamp:
            for step in range(startDateStamp, endDateStamp, 86400):
                oneday=time.strftime('%Y.%m.%d', time.localtime(step))
                index = "thelog-"+oneday
                if True == self.indexExist(index):
                    indexList.append(index)
            oneday=time.strftime('%Y.%m.%d', time.localtime(endDateStamp))
            index = "thelog-"+oneday
            if True == self.indexExist(index):
                indexList.append(index)
        else:
            return None

        indexstr=""
        if indexList is not None:
            indexstr = ",".join(indexList)
        return indexstr

    def SearchItem(self):

        t1 = "2022/10/19 09:00:44"
        t2 = "2022/10/21 23:39:44"
        indexstr = self.MakeIndex(t1, t2)

        role = ['sysadm', 'secadm', 'adtadm', 'operator', "admin"]
        operator = ""
        result = "成功"
        action = "修改用户状态"
        #action =  ""
        path = "/fluentd/log/theweb.log"
        timeitem = "exectime"
        sortitem = "path" 
        ascdesc = "asc"
        pgstart=0
        pgsize=10
        ip = ""

        conditions = []
        if role:
            condition_role = {"terms": { "role": role}}
            conditions.append(condition_role)
        if operator.strip():
            condition_operator = {"term": {"operator": operator}}
            conditions.append(condition_operator)
        if result.strip():
            condition_result = {"term": {"result.keyword": result}}
            conditions.append(condition_result)
        if action.strip():
            condition_action = {"term": {"action.keyword": action}}
            conditions.append(condition_action)
        if ip.strip():
            condition_ip = {"term": {"client_address": ip}}
            conditions.append(condition_ip)
        if path.strip():
            name = path.split("/", 3)[3]
            condition_path = {"wildcard": {"path": "*"+ name}}
            conditions.append(condition_path)
        if not conditions:
            nocondition={"match_all": {}}
            conditions.append(nocondition)

        body = {
            "query": {
                "bool": {
                    "must": conditions,
                    "filter": {
                        "range": {
                            timeitem: {
                                "gte": t1,
                                "lte": t2,
                            }
                        }
                    }
                }
            },
            "sort":[{
                #sortitem +".keyword": {
                "exectime": {
                    "order":ascdesc
                 }
            }],
            "from": pgstart,
            "size": pgsize 
        }
        print(body)
        print(indexstr)
        self.result = self.handler.search(index=indexstr, doc_type='fluentd', body=body)
        print(self.result["hits"])

    def CountItem(self):
        t1 = "2022/10/20 10:00:00"
        t2 = "2022/10/20 11:00:00"
        #return
        body = {
            "query": {
                "bool": {
                    "must": [
                        {"terms": {
                            "role": [ "admin", "sysadm"]
                        }},
                        {"term": {
                            "operator": "admin"
                        }},
                        {"term": {
                            "result": "ok"
                        }},
                        {"term": {
                            "action": "login"
                        }},
                        {"wildcard": {
                            "path": "*theauth.log"
                        }}
                    ],
                    "filter": {
                        "range": {
                            "exectime": {
                                "gte": t1,
                                "lte": t2,
                            }
                        }
                    }
                }
            }
        }
        self.result = self.handler.count(index=self.index, doc_type='fluentd', body=body)
        print(self.result)


    def get_page_data(self, result):
        for hit in result['hits']['hits']:
              print(hit)

    def UniquePath(self):
        self.AllField()
        self.array.clear()
        for hit in self.result['hits']['hits']:
            #self.array.append(os.path.basename(hit['_source']['path']))
            self.array.append(hit['_source']['path'])
        self.uniquePath = list(set(self.array))
        #print(self.uniquePath)
        return {"path":self.uniquePath}

    def UniqueResult(self):
        self.AllField()
        self.array.clear()
        for hit in self.result['hits']['hits']:
            self.array.append(hit['_source']['result'])
        self.uniqueResult = list(set(self.array))
        #print(self.uniqueResult)
        return {"result":self.uniqueResult}

    def UniqueAction(self):
        self.AllField()
        self.array.clear()
        print(self.result['hits']['hits'])
        for hit in self.result['hits']['hits']:
            self.array.append(hit['_source']['action'])
        self.uniqueAction = list(set(self.array))
        #print(self.uniqueAction)
        return {"action":self.uniqueAction}



if __name__=='__main__':
    #index='thelog-2022.06.28'
    index='thelog-*'
    index='thelog-2022.10.20,thelog-2022.10.21*'
    es = ESUtil(index, "192.168.1.209", 9200, 3600)
    #print(es.UniquePath())
    #print(es.UniqueResult())
    #print(es.UniqueAction())
    #es.AllField()
    #for index in es.handler.indices.get('thelog-*'):
    #    print(index)
    #allindexs = es.handler.indices.get('thelog-*').keys()
    #print(allindexs)
    #r = es.handler.indices.get('thelog-*').get("thelog-2022.10.20")
    #print(r)
    #print(es.handler.indices.get('thelog-*').keys())
    #print(es.handler.indices.get('thelog-*').values())
    #print(es.handler.indices.get('thelog-*').items())
    es.SearchItem()
    #es.CountItem()
