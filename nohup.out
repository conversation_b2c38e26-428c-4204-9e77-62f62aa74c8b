SLF4J: Failed to load class "org.slf4j.impl.StaticLoggerBinder".
SLF4J: Defaulting to no-operation (NOP) logger implementation
SLF4J: See http://www.slf4j.org/codes.html#StaticLoggerBinder for further details.

(Eclipse:4688): Gtk-CRITICAL **: 10:33:19.927: gtk_box_gadget_distribute: assertion 'size >= 0' failed in GtkScrollbar

(Eclipse:4688): Gtk-CRITICAL **: 10:33:20.392: gtk_box_gadget_distribute: assertion 'size >= 0' failed in GtkScrollbar

(Eclipse:4688): Gtk-CRITICAL **: 10:33:20.697: gtk_box_gadget_distribute: assertion 'size >= 0' failed in GtkScrollbar

(Eclipse:4688): Gtk-CRITICAL **: 10:33:20.699: gtk_box_gadget_distribute: assertion 'size >= 0' failed in GtkScrollbar

(Eclipse:4688): Gtk-CRITICAL **: 11:05:43.214: gtk_box_gadget_distribute: assertion 'size >= 0' failed in GtkScrollbar

(Eclipse:4688): Gtk-CRITICAL **: 11:05:43.237: gtk_box_gadget_distribute: assertion 'size >= 0' failed in GtkScrollbar

(Eclipse:4688): Gtk-CRITICAL **: 11:05:46.064: gtk_box_gadget_distribute: assertion 'size >= 0' failed in GtkScrollbar

(Eclipse:4688): Gtk-CRITICAL **: 11:05:46.074: gtk_box_gadget_distribute: assertion 'size >= 0' failed in GtkScrollbar

(Eclipse:4688): Gtk-CRITICAL **: 11:05:46.097: gtk_box_gadget_distribute: assertion 'size >= 0' failed in GtkScrollbar

(Eclipse:4688): Gtk-CRITICAL **: 11:05:46.097: gtk_box_gadget_distribute: assertion 'size >= 0' failed in GtkScrollbar

(Eclipse:4688): Gtk-CRITICAL **: 11:05:46.107: gtk_box_gadget_distribute: assertion 'size >= 0' failed in GtkScrollbar

(Eclipse:4688): Gtk-CRITICAL **: 11:05:46.128: gtk_box_gadget_distribute: assertion 'size >= 0' failed in GtkScrollbar

(Eclipse:4688): Gtk-CRITICAL **: 11:05:46.128: gtk_box_gadget_distribute: assertion 'size >= 0' failed in GtkScrollbar

(Eclipse:4688): Gtk-CRITICAL **: 11:05:46.131: gtk_box_gadget_distribute: assertion 'size >= 0' failed in GtkScrollbar

(Eclipse:4688): Gtk-CRITICAL **: 11:05:46.131: gtk_box_gadget_distribute: assertion 'size >= 0' failed in GtkScrollbar

(Eclipse:4688): Gtk-CRITICAL **: 11:05:46.131: gtk_box_gadget_distribute: assertion 'size >= 0' failed in GtkScrollbar

(Eclipse:4688): Gtk-CRITICAL **: 11:05:46.135: gtk_box_gadget_distribute: assertion 'size >= 0' failed in GtkScrollbar

(Eclipse:4688): Gtk-CRITICAL **: 11:05:46.143: gtk_box_gadget_distribute: assertion 'size >= 0' failed in GtkScrollbar

(Eclipse:4688): Gtk-CRITICAL **: 13:11:12.897: gtk_box_gadget_distribute: assertion 'size >= 0' failed in GtkScrollbar

(Eclipse:4688): Gtk-CRITICAL **: 13:11:12.901: gtk_box_gadget_distribute: assertion 'size >= 0' failed in GtkScrollbar

(Eclipse:4688): Gtk-CRITICAL **: 13:11:12.930: gtk_box_gadget_distribute: assertion 'size >= 0' failed in GtkScrollbar

(Eclipse:4688): Gtk-CRITICAL **: 13:11:12.930: gtk_box_gadget_distribute: assertion 'size >= 0' failed in GtkScrollbar

(Eclipse:4688): Gtk-CRITICAL **: 13:11:12.934: gtk_box_gadget_distribute: assertion 'size >= 0' failed in GtkScrollbar

(Eclipse:4688): Gtk-CRITICAL **: 13:11:12.935: gtk_box_gadget_distribute: assertion 'size >= 0' failed in GtkScrollbar

(Eclipse:4688): Gtk-CRITICAL **: 13:11:12.964: gtk_box_gadget_distribute: assertion 'size >= 0' failed in GtkScrollbar

(Eclipse:4688): Gtk-CRITICAL **: 13:11:12.964: gtk_box_gadget_distribute: assertion 'size >= 0' failed in GtkScrollbar

(Eclipse:4688): Gtk-CRITICAL **: 13:11:12.968: gtk_box_gadget_distribute: assertion 'size >= 0' failed in GtkScrollbar

(Eclipse:4688): Gtk-CRITICAL **: 13:11:12.969: gtk_box_gadget_distribute: assertion 'size >= 0' failed in GtkScrollbar

(Eclipse:4688): Gtk-CRITICAL **: 13:11:12.997: gtk_box_gadget_distribute: assertion 'size >= 0' failed in GtkScrollbar

(Eclipse:4688): Gtk-CRITICAL **: 13:11:13.670: gtk_box_gadget_distribute: assertion 'size >= 0' failed in GtkScrollbar

(Eclipse:4688): Gtk-CRITICAL **: 13:11:13.676: gtk_box_gadget_distribute: assertion 'size >= 0' failed in GtkScrollbar

(Eclipse:4688): Gtk-CRITICAL **: 13:11:13.676: gtk_box_gadget_distribute: assertion 'size >= 0' failed in GtkScrollbar

(Eclipse:4688): Gtk-CRITICAL **: 13:11:13.699: gtk_box_gadget_distribute: assertion 'size >= 0' failed in GtkScrollbar

(Eclipse:4688): Gtk-CRITICAL **: 13:11:13.699: gtk_box_gadget_distribute: assertion 'size >= 0' failed in GtkScrollbar

(Eclipse:4688): Gtk-CRITICAL **: 13:11:13.703: gtk_box_gadget_distribute: assertion 'size >= 0' failed in GtkScrollbar

(Eclipse:4688): Gtk-CRITICAL **: 13:11:13.703: gtk_box_gadget_distribute: assertion 'size >= 0' failed in GtkScrollbar

(Eclipse:4688): Gtk-CRITICAL **: 13:11:13.704: gtk_box_gadget_distribute: assertion 'size >= 0' failed in GtkScrollbar

(Eclipse:4688): Gtk-CRITICAL **: 13:11:13.730: gtk_box_gadget_distribute: assertion 'size >= 0' failed in GtkScrollbar

(Eclipse:4688): Gtk-CRITICAL **: 13:11:13.730: gtk_box_gadget_distribute: assertion 'size >= 0' failed in GtkScrollbar

(Eclipse:4688): Gtk-CRITICAL **: 13:11:13.735: gtk_box_gadget_distribute: assertion 'size >= 0' failed in GtkScrollbar

(Eclipse:4688): Gtk-CRITICAL **: 13:11:13.757: gtk_box_gadget_distribute: assertion 'size >= 0' failed in GtkScrollbar
