# -*- coding: utf-8 -*-
# coding=utf-8
from io import StringIO
import tornado.ioloop
import pyrestful.rest
from pyrestful import mediatypes
from pyrestful.rest import get, post, put, delete

from model.user import *
from util.cov import todict, serialize, theevent

from tornado_swagger.components import components
from mydoc.users import *
from db.model.user import User
from db.model.vm import VmGroup
from db.model.event import Event
import bcrypt
from sqlalchemy.orm import defer
from sqlalchemy.orm import undefer
import logging
logger = logging.getLogger(__name__)
from datetime import date, timedelta, datetime
import time
from dateutil.relativedelta import relativedelta 
import requests
import json
import os
from util.queryES import ESUtil
import settings
import logging


class LogconfHandler(pyrestful.rest.RestHandler):

    es = ""
    def set_default_headers(self):
        self.set_header("Access-Control-Allow-Origin", "http://127.0.0.1:8088")
        #self.set_header("Access-Control-Allow-Origin", "*")
        self.set_header("Access-Control-Allow-Headers", "X-Requested-With, Content-Type, Accept")
        self.set_header('Access-Control-Allow-Credentials', 'true')
        self.set_header('Access-Control-Allow-Methods', 'POST, PUT, GET, DELETE, OPTIONS')
        #self.set_header("Access-Control-Allow-Headers", "access-control-allow-origin,authorization,content-type")



    @get(_path="/v1/log/config", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def thecloud_get_all_config(self):
        index='thelog-*'
        es = ESUtil(settings.ESHOST, 9200, 3600)
        es.setIndex(index)
        print(es.UniquePath())
        print(es.UniqueResult())
        print(es.UniqueAction())

        return {"path": es.UniquePath()["path"], "result": es.UniqueResult()["result"],
                "action": es.UniqueAction()["action"]}


class LogHandler(pyrestful.rest.RestHandler):
    
    es = ""   
    def set_default_headers(self):
        self.set_header("Access-Control-Allow-Origin", "http://127.0.0.1:8088")
        #self.set_header("Access-Control-Allow-Origin", "*")
        self.set_header("Access-Control-Allow-Headers", "X-Requested-With, Content-Type, Accept")
        self.set_header('Access-Control-Allow-Credentials', 'true')
        self.set_header('Access-Control-Allow-Methods', 'POST, PUT, GET, DELETE, OPTIONS')
        #self.set_header("Access-Control-Allow-Headers", "access-control-allow-origin,authorization,content-type")
        



    # @get(_path="/v1/log/config", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    # def thecloud_get_all_config(self):
    #     index='thelog-*'
    #     es = ESUtil(settings.ESHOST, 9200, 3600)
    #     es.setIndex(index)
    #     print(es.UniquePath())
    #     print(es.UniqueResult())
    #     print(es.UniqueAction())
    #
    #     return {"path": es.UniquePath()["path"], "result": es.UniqueResult()["result"],
    #             "action": es.UniqueAction()["action"]}


    @get(_path="/v1/log/download", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def thecloud_get_all_config(self):


        def validate_date(date_text):
            try:
                datetime.strptime(date_text, '%Y/%m/%d %H:%M:%S')  # 假设日期格式为'YYYY-MM-DD'
                return True
            except ValueError:
                return False
    
    
        start = self.request.arguments.get("exectime", [b""])[0].decode("utf-8")
        end = self.request.arguments.get("end_date", [b""])[0].decode("utf-8")


        if not(validate_date(start) and validate_date(end)):
            # 处理无效输入
            return {"msg": "date format error"}


        data = {
            "action": "",
            "role": "",
            "path": "",
            "result": "",
            "operator": "",
            "exectime": start,
            "end_date": end,
            "page": 1,
            "pagecount": 5000,
            "search_str": "",
            "order_type": "desc",
            "order_by": ""
        }  # 替换为你要发送的数据

        result = custem_thecloud_post_all_log(self, data)

        # 将查询结果写入TXT文件
        output_txt = StringIO()
        output_txt.write(f"时间, 登录账号, 角色, 来源, 操作类型, 结果\n")
        for hit in result['hits']:
            source = hit['_source']
            if source['path'] == "/fluentd/log/theauth.log":
                source['path'] = "认证服务"
            if source['path'] == "/fluentd/log/theweb.log":
                source['path'] = "web服务"
            if source['path'] == "/fluentd/log/theuser.log":
                source['path'] = "用户服务"
            if source['path'] == "/fluentd/log/theupload.log":
                source['path'] = "上传服务"
            if source['path'] == "/fluentd/log/thevdiserver.log":
                source['path'] = "云桌面服务"
            output_txt.write(f"{source['exectime']}, {source['operator']}, {source['role']}, {source['path']},{source['action']}, {source['result']}\n")

        # 将光标移到文件开头
        output_txt.seek(0)

        # 返回生成的TXT文件供下载
        return self.custom_send_file(output_txt)

    def custom_send_file(self,file_data):
        json_bytes = file_data.getvalue().encode('utf-8-sig')

        # 设置响应头，告诉浏览器将文件作为附件下载
        self.set_header("Content-Disposition", "attachment; filename=log_data.csv")
        self.set_header("Content-Type", "text/csv")
        self.set_header("Content-Length", len(json_bytes))

        # 发送 JSON 数据给客户端
        self.write(json_bytes)
        self.finish()

    def indexExist(self, index):
        for i in self.es.handler.indices.get('thelog-*'):
            if i == index:
                return True
        return False

    def makeIndex(self, starttime, endtime):
        startTimeArray = time.strptime(starttime, "%Y/%m/%d %H:%M:%S")
        endTimeArray = time.strptime(endtime, "%Y/%m/%d %H:%M:%S")
        startTimeStamp = str(int(time.mktime(startTimeArray)))+"000"
        endTimeStamp = str(int(time.mktime(endTimeArray)))+"000"

        startIndexTime = time.strftime("%Y.%m.%d", startTimeArray)
        endIndexTime = time.strftime("%Y.%m.%d", endTimeArray)

        startDateArray = time.strptime(startIndexTime, "%Y.%m.%d")
        endDateArray = time.strptime(endIndexTime, "%Y.%m.%d")
        startDateStamp = int(time.mktime(startDateArray))
        endDateStamp = int(time.mktime(endDateArray))

        indexList = []
        if endDateStamp == startDateStamp:
            oneday=time.strftime('%Y.%m.%d', time.localtime(startDateStamp))
            index = "thelog-"+oneday
            if True == self.indexExist(index):
                indexList.append(index)
        elif endDateStamp > startDateStamp:
            for step in range(startDateStamp, endDateStamp, 86400):
                oneday=time.strftime('%Y.%m.%d', time.localtime(step))
                index = "thelog-"+oneday
                if True == self.indexExist(index):
                    indexList.append(index)
            oneday=time.strftime('%Y.%m.%d', time.localtime(endDateStamp))
            index = "thelog-"+oneday
            if True == self.indexExist(index):
                indexList.append(index)
        else:
            return None

        indexstr=""
        if indexList:
            indexstr = ",".join(indexList)
        return indexstr
    
    @post(_path="/v1/log/all", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def thecloud_post_all_log(self, form):
        action = form.get("action", "")
        role = form.get("role", "")
        path = form.get("path", "")
        operator = form.get("operator", "")
        
        end_str = (datetime.now() + timedelta(hours=8)).strftime("%Y/%m/%d %H:%M:%S")
        start_str = (datetime.now() - timedelta(days=1) + timedelta(hours=8)).strftime("%Y/%m/%d %H:%M:%S")
        
        starttime = form.get("exectime", start_str)
        if starttime == "":
            starttime = start_str
        
        endtime = form.get("end_date", end_str)
        if endtime == "":
            endtime = end_str
        
        result = form.get("result")
        sortitem = form.get("order_by", "exectime")
        if sortitem == "":
            sortitem = "exectime"
        if sortitem != "exectime":
            sortitem = sortitem +".keyword"
        ascdesc = form.get("order_type", "desc")
        
        if ascdesc not in ["desc", "asc"]:
            ascdesc = "desc"
        client_address = form.get("ip", "")
        pgsize = int(form.get("pagecount"))
        pgstart = (int(form.get("page"))-1) * pgsize
        timeitem = "exectime"

        #ac = {
        #    "sysadm": ["sysadm", "adtadm", "secadm", "operator"],
        #    "adtadm": ["sysadm", "secadm" ],
        #    "secadm": ["adtadm", "operator"]
        #}
 
        #operator = ""
        #operator  = self.get_cookie("username")
 
        conditions = []
        if role.strip():
            condition_role = {"term": {"role": role}}
            conditions.append(condition_role)
        if operator.strip():
            condition_operator = {"term": {"operator": operator}}
            conditions.append(condition_operator)
        if result.strip():
            condition_result = {"term": {"result.keyword": result}}
            conditions.append(condition_result)
        if action.strip():
            condition_action = {"term": {"action.keyword": action}}
            conditions.append(condition_action)
        if client_address.strip():
            condition_clientaddress = {"term": {"client_address": client_address}}
            conditions.append(condition_clientaddress)
        if path.strip():
            name = path.split("/", 3)[3]
            condition_path = {"wildcard": {"path": "*"+name}}
            conditions.append(condition_path)
        if not conditions:
            nocondition={"match_all": {}}
            conditions.append(nocondition)
        body = {
            "query": {
                "bool": {
                    "must": conditions,
                    "filter": {
                        "range": {
                            timeitem: {
                                "gte": starttime,
                                "lte": endtime,
                            }
                        }
                    }
                }
            },
            "sort":[{
                sortitem: {
                    "order":ascdesc
                 }
            }],
            "from": pgstart,
            "size": pgsize 
        }

        logging.warning(body)
        self.es = ESUtil(settings.ESHOST, 9200, 5)
        indexstr = self.makeIndex(starttime, endtime)
        logging.warning(indexstr)
        if indexstr.strip():
            result = self.es.handler.search(index=indexstr, body=body)
            logging.warning(result["hits"])
            return result["hits"]

        return {}


def custem_thecloud_post_all_log(self, form):
    action = form.get("action", "")
    role = form.get("role", "")
    path = form.get("path", "")
    operator = form.get("operator", "")

    end_str = (datetime.now() + timedelta(hours=8)).strftime("%Y/%m/%d %H:%M:%S")
    start_str = (datetime.now() - timedelta(days=1) + timedelta(hours=8)).strftime("%Y/%m/%d %H:%M:%S")

    starttime = form.get("exectime", start_str)
    if starttime == "":
        starttime = start_str

    endtime = form.get("end_date", end_str)
    if endtime == "":
        endtime = end_str

    result = form.get("result")
    sortitem = form.get("order_by", "exectime")
    if sortitem == "":
        sortitem = "exectime"
    if sortitem != "exectime":
        sortitem = sortitem + ".keyword"
    ascdesc = form.get("order_type", "desc")

    if ascdesc not in ["desc", "asc"]:
        ascdesc = "desc"
    client_address = form.get("ip", "")
    pgsize = int(form.get("pagecount"))
    pgstart = (int(form.get("page")) - 1) * pgsize
    timeitem = "exectime"

    # ac = {
    #    "sysadm": ["sysadm", "adtadm", "secadm", "operator"],
    #    "adtadm": ["sysadm", "secadm" ],
    #    "secadm": ["adtadm", "operator"]
    # }

    # operator = ""
    # operator  = self.get_cookie("username")

    conditions = []
    if role.strip():
        condition_role = {"term": {"role": role}}
        conditions.append(condition_role)
    if operator.strip():
        condition_operator = {"term": {"operator": operator}}
        conditions.append(condition_operator)
    if result.strip():
        condition_result = {"term": {"result.keyword": result}}
        conditions.append(condition_result)
    if action.strip():
        condition_action = {"term": {"action.keyword": action}}
        conditions.append(condition_action)
    if client_address.strip():
        condition_clientaddress = {"term": {"client_address": client_address}}
        conditions.append(condition_clientaddress)
    if path.strip():
        name = path.split("/", 3)[3]
        condition_path = {"wildcard": {"path": "*" + name}}
        conditions.append(condition_path)
    if not conditions:
        nocondition = {"match_all": {}}
        conditions.append(nocondition)
    body = {
        "query": {
            "bool": {
                "must": conditions,
                "filter": {
                    "range": {
                        timeitem: {
                            "gte": starttime,
                            "lte": endtime,
                        }
                    }
                }
            }
        },
        "sort": [{
            sortitem: {
                "order": ascdesc
            }
        }],
        "from": pgstart,
        "size": pgsize
    }

    logging.warning(body)
    self.es = ESUtil(settings.ESHOST, 9200, 5)
    indexstr = self.makeIndex(starttime, endtime)
    logging.warning(indexstr)
    if indexstr.strip():
        result = self.es.handler.search(index=indexstr, body=body)
        logging.warning(result["hits"])
        return result["hits"]

    return {}
