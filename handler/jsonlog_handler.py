# -*- coding: utf-8 -*-
# coding=utf-8
from io import StringIO

from dateutil import parser

import pyrestful.rest
from model.log import get_enum_values, OperationCategory, OperationResult, OperationRole, Action
from pyrestful import mediatypes
from pyrestful.rest import get, post
import logging
from datetime import date, timedelta, datetime
import time
from util.queryES import ESUtil
import settings

logger = logging.getLogger(__name__)


class JsonLogconfHandler(pyrestful.rest.RestHandler):
    def set_default_headers(self):
        self.set_header("Access-Control-Allow-Origin", "http://127.0.0.1:8088")
        self.set_header("Access-Control-Allow-Headers", "X-Requested-With, Content-Type, Accept")
        self.set_header('Access-Control-Allow-Credentials', 'true')
        self.set_header('Access-Control-Allow-Methods', 'POST, PUT, GET, DELETE, OPTIONS')

    @get(_path="/v2/log/config", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def thecloud_get_all_config_v2(self):
        index = 'jsonthelog-*'
        es = ESUtil(settings.ESHOST, 9200, 3600)
        es.setIndex(index)

        return {
            # "path": es.UniquePath()["path"],
            "category": es.UniqueCategory()["category"],
            "action": es.UniqueAction()["action"],
            "role": es.UniqueRole()["role"],
            "result": es.UniqueResult()["result"]
        }

    @get(_path="/v3/log/config", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def thecloud_get_all_config_v3(self):

        return {
            "category": get_enum_values(OperationCategory),
            "result": get_enum_values(OperationResult),
            "role": get_enum_values(OperationRole),
            "action": get_enum_values(Action),
        }


class JsonLogHandler(pyrestful.rest.RestHandler):
    def set_default_headers(self):
        self.set_header("Access-Control-Allow-Origin", "http://127.0.0.1:8088")
        self.set_header("Access-Control-Allow-Headers", "X-Requested-With, Content-Type, Accept")
        self.set_header('Access-Control-Allow-Credentials', 'true')
        self.set_header('Access-Control-Allow-Methods', 'POST, PUT, GET, DELETE, OPTIONS')

    def validate_date(self, date_text):
        try:
            datetime.strptime(date_text, '%Y/%m/%d %H:%M:%S')
            return True
        except ValueError:
            return False

    @get(_path="/v2/log/download", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def thecloud_download_all_config_v2(self):
        start = self.request.arguments.get("exectime", [b""])[0].decode("utf-8")
        end = self.request.arguments.get("end_date", [b""])[0].decode("utf-8")

        if not (self.validate_date(start) and self.validate_date(end)):
            return {"msg": "date format error"}

        data = {
            "action": "",
            "role": "",
            "path": "",
            "result": "",
            "category": "",
            "exectime": start,
            "end_date": end,
            "page": 1,
            "pagecount": 5000,
            "search_str": "",
            "order_type": "desc",
            "order_by": ""
        }

        result = custom_thecloud_post_all_log(self, data)
        return self.generate_txt(result)

    def generate_txt(self, result):
        output_txt = StringIO()
        output_txt.write("时间, 类别, 角色, 操作, 结果, 详细信息\n")
        path_map = {
            "/fluentd/log/jsontheauth.log": "认证服务",
            "/fluentd/log/jsontheweb.log": "web服务",
            "/fluentd/log/jsontheuser.log": "用户服务",
            "/fluentd/log/jsontheupload.log": "上传服务",
            "/fluentd/log/jsonthevdiserver.log": "云桌面服务"
        }

        if 'hits' not in result or not result['hits']:
            output_txt.seek(0)
            return self.custom_send_file(output_txt)
        for hit in result['hits']:
            source = hit['_source']
            dt = parser.isoparse(source['@timestamp'])

            formatted_datetime_str = dt.strftime("%Y-%m-%d %H:%M:%S")
            # source['path'] = path_map.get(source['path'], source['path'])
            output_txt.write(
                # f"{source['@timestamp']}, {source['category']}, {source['role']}, {source['path']},{source['action']}, {source['result']}, {source['description']}\n")
                f"{formatted_datetime_str}, {source['category']}, {source['role']},{source['action']}, {source['result']}, {source['description']}\n")

        output_txt.seek(0)
        return self.custom_send_file(output_txt)

    def custom_send_file(self, file_data):
        json_bytes = file_data.getvalue().encode('utf-8-sig')
        self.set_header("Content-Disposition", "attachment; filename=log_data.csv")
        self.set_header("Content-Type", "text/csv")
        self.set_header("Content-Length", len(json_bytes))
        self.write(json_bytes)
        self.finish()

    def indexExist(self, index):
        return index in self.es.handler.indices.get('jsonthelog-*')

    def makeIndex1(self, starttime, endtime):
        start_ts = int(time.mktime(time.strptime(starttime, "%Y/%m/%d %H:%M:%S"))) * 1000
        end_ts = int(time.mktime(time.strptime(endtime, "%Y/%m/%d %H:%M:%S"))) * 1000

        start_index = time.strftime("%Y.%m.%d", time.localtime(start_ts / 1000))
        end_index = time.strftime("%Y.%m.%d", time.localtime(end_ts / 1000))

        index_list = []
        for ts in range(start_ts, end_ts + 1, 86400000):
            index = "jsonthelog-" + time.strftime('%Y.%m.%d', time.localtime(ts / 1000))
            if self.indexExist(index):
                index_list.append(index)

        return ",".join(index_list) if index_list else None

    @post(_path="/v2/log/all", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def thecloud_post_all_log(self, form):
        starttime = form.get("exectime", (datetime.now() - timedelta(days=1)).strftime("%Y/%m/%d %H:%M:%S"))
        endtime = form.get("end_date", datetime.now().strftime("%Y/%m/%d %H:%M:%S"))

        gt = datetime.strptime(starttime, "%Y/%m/%d %H:%M:%S")
        start_time = gt.strftime("%Y-%m-%dT%H:%M:%S")
        lt = datetime.strptime(endtime, "%Y/%m/%d %H:%M:%S")
        end_time = lt.strftime("%Y-%m-%dT%H:%M:%S")

        sortitem = form.get("order_type", "desc")

        # 动态指定过滤字段
        must_clauses = []

        # 添加非空的字段过滤条件
        for field in ["category", "action", "role", "path", "result", "username"]:
            if form[field]:
                must_clauses.append({"match_phrase": {field: form[field]}})

        # 添加搜索字符串
        if form["search_str"]:
            must_clauses.append({
                "multi_match": {
                    "query": form["search_str"],
                    "fields": ["category", "action", "role", "path", "result", "username"]
                }
            })

        # 添加时间范围过滤
        must_clauses.append({
            "range": {
                "@timestamp": {
                    "gte": start_time,
                    "lte": end_time,
                    "format": "strict_date_optional_time||epoch_millis"
                }
            }
        })

        # 构建查询主体
        query_body = {
            "query": {
                "bool": {
                    "must": must_clauses
                }
            },
            "sort": [
                {
                    "@timestamp": {
                        "order": sortitem
                    }
                }
            ],
            "from": (form["page"] - 1) * form["pagecount"],
            "size": form["pagecount"]
        }

        self.es = ESUtil(settings.ESHOST, 9200, 5)
        indexstr = self.makeIndex1(starttime, endtime)
        if indexstr:
            result = self.es.handler.search(index=indexstr, body=query_body)
            return result["hits"]
        return {}


    @post(_path="/v2/log/with/user/all", _consumes=mediatypes.APPLICATION_JSON, _produces=mediatypes.APPLICATION_JSON)
    def thecloud_post_all_log_with_user(self, form):
        role = self.get_cookie("role", "")
        username = self.get_cookie("username", "")
        starttime = form.get("exectime", (datetime.now() - timedelta(days=1)).strftime("%Y/%m/%d %H:%M:%S"))
        endtime = form.get("end_date", datetime.now().strftime("%Y/%m/%d %H:%M:%S"))

        gt = datetime.strptime(starttime, "%Y/%m/%d %H:%M:%S")
        start_time = gt.strftime("%Y-%m-%dT%H:%M:%S")
        lt = datetime.strptime(endtime, "%Y/%m/%d %H:%M:%S")
        end_time = lt.strftime("%Y-%m-%dT%H:%M:%S")

        sortitem = form.get("order_type", "desc")

        # 动态指定过滤字段
        must_clauses = []

        # 添加非空的字段过滤条件
        for field in ["category", "action", "role", "path", "result", "username"]:
            if form[field]:
                must_clauses.append({"match_phrase": {field: form[field]}})

        # 添加搜索字符串
        if form["search_str"]:
            must_clauses.append({
                "multi_match": {
                    "query": form["search_str"],
                    "fields": ["category", "action", "role", "path", "result", "username"]
                }
            })

        # 添加时间范围过滤
        must_clauses.append({
            "range": {
                "@timestamp": {
                    "gte": start_time,
                    "lte": end_time,
                    "format": "strict_date_optional_time||epoch_millis"
                }
            }
        })

        # 构建查询主体
        query_body = {
            "query": {
                "bool": {
                    "must": must_clauses
                }
            },
            "sort": [
                {
                    "@timestamp": {
                        "order": sortitem
                    }
                }
            ],
            "from": (form["page"] - 1) * form["pagecount"],
            "size": form["pagecount"]
        }

        self.es = ESUtil(settings.ESHOST, 9200, 5)
        indexstr = self.makeIndex1(starttime, endtime)
        if indexstr:
            result = self.es.handler.search(index=indexstr, body=query_body)
            return result["hits"]
        return {}


def custom_thecloud_post_all_log(self, form):
    starttime = form.get("exectime", (datetime.now() - timedelta(days=1)).strftime("%Y/%m/%d %H:%M:%S"))
    endtime = form.get("end_date", datetime.now().strftime("%Y/%m/%d %H:%M:%S"))

    gt = datetime.strptime(starttime, "%Y/%m/%d %H:%M:%S")
    start_time = gt.strftime("%Y-%m-%dT%H:%M:%S")
    lt = datetime.strptime(endtime, "%Y/%m/%d %H:%M:%S")
    end_time = lt.strftime("%Y-%m-%dT%H:%M:%S")

    sortitem = form.get("order_type", "desc")

    # 动态指定过滤字段
    must_clauses = []

    # 添加非空的字段过滤条件
    for field in ["category", "action", "role", "path", "result"]:
        if form[field]:
            must_clauses.append({"match_phrase": {field: form[field]}})

    # 添加搜索字符串
    if form["search_str"]:
        must_clauses.append({
            "multi_match": {
                "query": form["search_str"],
                "fields": ["category", "action", "role", "path", "result"]
            }
        })

    # 添加时间范围过滤
    must_clauses.append({
        "range": {
            "@timestamp": {
                "gte": start_time,
                "lte": end_time,
                "format": "strict_date_optional_time||epoch_millis"
            }
        }
    })

    # 构建查询主体
    query_body = {
        "query": {
            "bool": {
                "must": must_clauses
            }
        },
        "sort": [
            {
                "@timestamp": {
                    "order": sortitem
                }
            }
        ],
        "from": (form["page"] - 1) * form["pagecount"],
        "size": form["pagecount"]
    }

    self.es = ESUtil(settings.ESHOST, 9200, 5)
    indexstr = self.makeIndex1(starttime, endtime)
    if indexstr:
        result = self.es.handler.search(index=indexstr, body=query_body)
        return result["hits"]
    return {}
