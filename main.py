# -*- coding: utf-8 -*-
import sys
import tornado.ioloop
import pyrestful.rest

from pyrestful import mediatypes
from pyrestful.rest import get, post, put, delete

from handler.log_handler import <PERSON><PERSON><PERSON><PERSON><PERSON>, Logconf<PERSON>andler
from handler.jsonlog_handler import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, JsonLogHandler

import settings as se

import tornado.log
import tornado.options
import logging.config
import os
import settings as se

logger = logging.getLogger(__name__)

tornado.options.define("access_to_stdout", default=True, help="Log tornado.access to stdout")

from datetime import datetime
from pytz import timezone
import logging


def timetz(*args):
    return datetime.now(tz).timetuple()


tz = timezone('Asia/Shanghai')  # UTC, Asia/Shanghai, Europe/Berlin


def init_logging(access_to_stdout=False):
    if access_to_stdout:
        access_log = logging.getLogger()
        # access_log = logging.getLogger('tornado.access')
        # access_log.propagate = False
        # make sure access log is enabled even if error level is WARNING|ERROR
        access_log.setLevel(logging.INFO)
        """
        stdout_handler = logging.StreamHandler(sys.stdout)
        
        my_log_format = '%(color)s::: %(levelname)s %(name)s %(asctime)s ::: %(module)s:%(lineno)d in %(funcName)s :::%(end_color)s\
                 \n %(message)s\n'
        log_formatter = tornado.log.LogFormatter(fmt=my_log_format, color=True)
        formatter = logging.Formatter(
            '[%(levelname)1.1s %(asctime)s.%(msecs)d '
            '%(module)s:%(lineno)d] %(message)s',
            "%Y-%m-%d %H:%M:%S"
        )
                
        stdout_handler.setFormatter(log_formatter)
        access_log.setLevel(logging.INFO)
        access_log.addHandler(stdout_handler)
        """

        log_file = se.LOG_USER_PATH
        file_handler = logging.handlers.TimedRotatingFileHandler(log_file, when="d", interval=1, backupCount=30)

        logging.Formatter.converter = timetz

        log_formatter = logging.Formatter(
            "%(asctime)s;%(module)s;%(levelname)s;%(lineno)d;%(message)s", datefmt='%Y/%m/%d %H:%M:%S',
        )
        file_handler.setFormatter(log_formatter)

        # 将处理器附加到根logger
        # root_logger = logging.getLogger()
        access_log.addHandler(file_handler)


def bootstrap():
    tornado.options.parse_command_line(final=True)
    init_logging(tornado.options.options.access_to_stdout)


if __name__ == '__main__':
    try:
        print("Start the log service")
        bootstrap()
        mysettings = dict(
            cookie_secret="some_long_secret_and_other_settins",
            DB_USERNAME=se.DB_USERNAME,
            DB_PASSWORD=se.DB_PASSWORD,
            DB_HOSTNAME=se.DB_HOSTNAME,
            DB_PORT=se.DB_PORT,
            DB_DATABASE=se.DB_DATABASE
        )
        app = pyrestful.rest.RestService([LogHandler, LogconfHandler, JsonLogconfHandler, JsonLogHandler], debug=True,
                                         **mysettings)
        if len(sys.argv) > 1:
            app.listen(sys.argv[1])
        else:
            app.listen(8093)
        tornado.ioloop.IOLoop.instance().start()
    except KeyboardInterrupt:
        print("\nStop the service")
